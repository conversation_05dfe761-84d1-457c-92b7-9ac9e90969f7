part of 'record_bloc.dart';

@immutable
sealed class RecordEvent {}

class InitializeRecording extends RecordEvent {
  final String userId;
  InitializeRecording(this.userId);
}

class StartRecording extends RecordEvent {
  Folder? folder;
  StartRecording(this.folder);
}

class StopRecording extends RecordEvent {}

class ResumeRecording extends RecordEvent {}

class DiscardRecording extends RecordEvent {}

class UpdateDuration extends RecordEvent {
  final int duration;
  UpdateDuration(this.duration);
}

// class UpdateFolder extends RecordEvent {
//   final String folder;
//   UpdateFolder(this.folder);
// }

class SaveProgress extends RecordEvent {}

class ForegroundTaskStopped extends RecordEvent {}

class UploadCompleted extends RecordEvent {}

class UploadFailed extends RecordEvent {
  final String error;
  UploadFailed(this.error);
}

class UploadProgress extends RecordEvent {
  final double progress;
  UploadProgress(this.progress);
}

class UploadStarted extends RecordEvent {}

class RetryFailedUploads extends RecordEvent {}
