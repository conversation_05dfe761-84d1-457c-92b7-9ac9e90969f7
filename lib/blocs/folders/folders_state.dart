part of 'folders_bloc.dart';

@immutable
sealed class FoldersState {}

final class FoldersInitial extends FoldersState {}

final class FoldersLoading extends FoldersState {}

final class FoldersCreated extends FoldersState {
  final Folder folder;

  FoldersCreated(this.folder);
}

final class FoldersLoaded extends FoldersState {
  final List<Folder> folders;
  FoldersLoaded(this.folders);
}

final class FoldersError extends FoldersState {
  final String message;
  FoldersError(this.message);
}

class FolderUpdated extends FoldersState {
  final Folder folder;
  FolderUpdated(this.folder);
}
