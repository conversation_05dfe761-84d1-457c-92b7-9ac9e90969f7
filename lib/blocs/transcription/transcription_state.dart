part of 'transcription_bloc.dart';

@immutable
class TranscriptionState {
  final PlayerState playerState;
  final String? localAudioPath;
  final bool isInitialized;
  final bool isLoading;
  final String? errorMessage;
  final Duration currentPosition;
  final Duration totalDuration;
  final bool isWaveformReady;
  final String searchQuery;
  final List<Transcription> filteredTranscriptions;
  final List<Transcription> groupedTranscriptions;

  const TranscriptionState({
    this.playerState = PlayerState.stopped,
    this.localAudioPath,
    this.isInitialized = false,
    this.isLoading = false,
    this.errorMessage,
    this.currentPosition = Duration.zero,
    this.totalDuration = Duration.zero,
    this.isWaveformReady = false,
    this.searchQuery = '',
    this.filteredTranscriptions = const [],
    this.groupedTranscriptions = const [],
  });

  TranscriptionState copyWith({
    PlayerState? playerState,
    String? localAudioPath,
    bool? isInitialized,
    bool? isLoading,
    String? errorMessage,
    Duration? currentPosition,
    Duration? totalDuration,
    bool? isWaveformReady,
    String? searchQuery,
    List<Transcription>? filteredTranscriptions,
    List<Transcription>? groupedTranscriptions,
  }) {
    return TranscriptionState(
      playerState: playerState ?? this.playerState,
      localAudioPath: localAudioPath ?? this.localAudioPath,
      isInitialized: isInitialized ?? this.isInitialized,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      isWaveformReady: isWaveformReady ?? this.isWaveformReady,
      searchQuery: searchQuery ?? this.searchQuery,
      filteredTranscriptions:
          filteredTranscriptions ?? this.filteredTranscriptions,
      groupedTranscriptions:
          groupedTranscriptions ?? this.groupedTranscriptions,
    );
  }

  bool get isPlaying => playerState == PlayerState.playing;
  bool get isPaused => playerState == PlayerState.paused;
  bool get isStopped => playerState == PlayerState.stopped;
}
