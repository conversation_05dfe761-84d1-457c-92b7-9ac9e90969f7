part of 'transcription_bloc.dart';

@immutable
sealed class TranscriptionEvent {}

class InitializeAudioPlayer extends TranscriptionEvent {
  final Audio audio;
  InitializeAudioPlayer(this.audio);
}

class Play<PERSON>udio extends TranscriptionEvent {}

class Pause<PERSON>udio extends TranscriptionEvent {}

class Stop<PERSON>udio extends TranscriptionEvent {}

class SeekAudio extends TranscriptionEvent {
  final Duration position;
  SeekAudio(this.position);
}

class UpdatePlayerPosition extends TranscriptionEvent {
  final Duration position;
  UpdatePlayerPosition(this.position);
}

class UpdatePlayerState extends TranscriptionEvent {
  final PlayerState playerState;
  UpdatePlayerState(this.playerState);
}

class SearchTranscript extends TranscriptionEvent {
  final String query;
  SearchTranscript(this.query);
}

class JumpToTranscript extends TranscriptionEvent {
  final Transcription transcription;
  JumpToTranscript(this.transcription);
}

class PlayerError extends TranscriptionEvent {
  final String error;
  PlayerError(this.error);
}

class WaveformReady extends TranscriptionEvent {}

class DisposePlayer extends TranscriptionEvent {}
