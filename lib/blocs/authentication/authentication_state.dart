part of 'authentication_bloc.dart';

@immutable
sealed class AuthenticationState {}

final class AuthenticationInitial extends AuthenticationState {}

final class AuthLoading extends AuthenticationState {}

final class Authenticated extends AuthenticationState {
  final User user;
  Authenticated(this.user);
}

final class Unauthenticated extends AuthenticationState {}

final class AuthError extends AuthenticationState {
  final String message;
  AuthError(this.message);
}
