import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:sluqe/models/audio.dart';
import 'package:sluqe/services/audio/audio_services.dart';

part 'audio_event.dart';
part 'audio_state.dart';

class AudioBloc extends Bloc<AudioEvent, AudioState> {
  final AudioServices _audioServices;

  AudioBloc({AudioServices? audioServices})
    : _audioServices = audioServices ?? AudioServices(),
      super(AudioInitial()) {
    on<GetAllAudiosEvent>((event, emit) async {
      emit(AudioLoading());

      try {
        final audios = await _audioServices.getAllAudios(event.userId);
        emit(AudioSuccess(audios));
      } catch (e) {
        emit(AudioError(e.toString()));
      }
    });

    on<SearchAudioEvent>((event, emit) async {
      emit(AudioLoading());

      try {
        final audios = await _audioServices.searchAudios(
          event.searchQuery,
          event.userId,
        );
        emit(AudioSuccess(audios));
      } catch (e) {
        emit(AudioError(e.toString()));
      }
    });
  }
}
