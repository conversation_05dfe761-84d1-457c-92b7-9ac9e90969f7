import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';

class UrlHandlerService {
  static final UrlHandlerService _instance = UrlHandlerService._internal();
  factory UrlHandlerService() => _instance;
  UrlHandlerService._internal();

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;
  Function()? _onStopRecording;

  void initialize({Function()? onStopRecording}) {
    _appLinks = AppLinks();
    _onStopRecording = onStopRecording;
    _initDeepLinks();
  }

  void _initDeepLinks() {
    // Handle app launch from deep link
    _appLinks.getInitialLink().then((Uri? uri) {
      if (uri != null) {
        _handleDeepLink(uri);
      }
    });

    // Handle deep links while app is running
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (Uri uri) {
        _handleDeepLink(uri);
      },
      onError: (err) {
        debugPrint('Deep link error: $err');
      },
    );
  }

  void _handleDeepLink(Uri uri) {
    debugPrint('Received deep link: $uri');
    
    if (uri.scheme == 'sluqe') {
      switch (uri.host) {
        case 'stop-recording':
          _handleStopRecording();
          break;
        default:
          debugPrint('Unknown deep link action: ${uri.host}');
      }
    }
  }

  void _handleStopRecording() {
    debugPrint('Stop recording requested from Dynamic Island');
    if (_onStopRecording != null) {
      _onStopRecording!();
    }
  }

  void dispose() {
    _linkSubscription?.cancel();
  }
}
