import 'dart:async';
import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:sluqe/models/folder.dart';
import 'package:uuid/uuid.dart';

class UploadService {
  Future<void> uploadRecording(
    String path,
    String userId,
    Folder? folder, {
    Function(double)? onProgress,
  }) async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        await _performUpload(path, userId, folder, onProgress: onProgress);
        return;
      } catch (e) {
        retryCount++;
        if (retryCount < maxRetries) {
          await Future.delayed(Duration(seconds: 2 * retryCount));
        }
      }
    }
    throw Exception('Upload failed after $maxRetries attempts');
  }

  Future<void> _performUpload(
    String path,
    String userId,
    Folder? folder, {
    Function(double)? onProgress,
  }) async {
    final audioFile = File(path);
    final audioId = Uuid().v4();
    final storageRef = FirebaseStorage.instance
        .ref()
        .child('audios')
        .child(userId)
        .child(audioId);

    if (!await audioFile.exists()) {
      throw Exception('File does not exist: $path');
    }

    final bytes = await audioFile.readAsBytes();

    try {
      final uploadTask = storageRef.putData(
        bytes,
        SettableMetadata(
          contentType: 'audio/m4a',
          customMetadata: folder != null ? {'folderId': folder.id!} : null,
        ),
      );

      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress(progress);
        });
      }

      await uploadTask.timeout(
        const Duration(minutes: 2),
        onTimeout: () {
          throw Exception('Upload timeout after 2 minutes');
        },
      );
    } on FirebaseException {
      rethrow;
    }
  }
}
