import 'dart:async';
import 'dart:io';
import 'package:live_activities/live_activities.dart';

class IOSLiveActivityService {
  static bool _isSupported = false;
  static String? _currentActivityId;
  static LiveActivities? _liveActivities;

  static Future<void> initialize() async {
    if (!Platform.isIOS) return;

    try {
      _liveActivities = LiveActivities();

      // Initialize with app group (required for Live Activities)
      await _liveActivities!.init(
        appGroupId: 'group.io.boet.sluqe.recording',
      );

      _isSupported = await _checkDynamicIslandSupport();
      print('Live Activities initialized successfully');
    } catch (e) {
      print('Failed to initialize Live Activities: $e');
      _isSupported = false;
    }
  }

  static Future<bool> _checkDynamicIslandSupport() async {
    return Platform.isIOS;
  }

  static Future<void> startRecordingActivity({
    required String duration,
    required Function() onStopPressed,
  }) async {
    if (!_isSupported || !Platform.isIOS || _liveActivities == null) {
      print('Live Activity not supported or not initialized');
      return;
    }

    try {
      final activityData = {
        'duration': duration,
        'isRecording': true,
        'appName': 'Sluqe',
      };

      final activity = await _liveActivities!.createActivity(activityData);
      _currentActivityId = activity;
      print('Live Activity started with ID: $_currentActivityId');
    } catch (e) {
      print('Failed to start Live Activity: $e');
      print('This is normal if Live Activities are disabled in iOS Settings');
      _currentActivityId = null;
      _isSupported = false; // Disable further attempts
    }
  }

  static Future<void> updateRecordingActivity(String duration) async {
    if (!_isSupported || _currentActivityId == null || _liveActivities == null) {
      // Silently fail if Live Activity is not available
      return;
    }

    try {
      final activityData = {
        'duration': duration,
        'isRecording': true,
        'appName': 'Sluqe',
      };

      await _liveActivities!.updateActivity(
        _currentActivityId!,
        activityData,
      );
      // Success - no need to log
    } catch (e) {
      // Silently fail - Live Activities might be disabled
      _isSupported = false; // Disable further attempts
    }
  }

  static Future<void> stopRecordingActivity() async {
    if (!_isSupported || _currentActivityId == null || _liveActivities == null) return;

    try {
      await _liveActivities!.endActivity(_currentActivityId!);
      _currentActivityId = null;
    } catch (e) {
      _currentActivityId = null;
    }
  }

  static bool get isSupported => _isSupported;
  static bool get hasActiveActivity => _currentActivityId != null;
}
