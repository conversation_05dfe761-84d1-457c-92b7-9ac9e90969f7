import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/models/recording_progress.dart';

class StorageService {
  static const String _recordingPathKey = 'current_recording_path';
  static const String _durationKey = 'recording_duration';
  static const String _isActiveKey = 'is_recording_active';
  static const String _lastSaveTimeKey = 'last_save_time';
  static const String _recorderInitializedKey = 'recorder_initialized';
  static const String _failedUploadsKey = 'failed_uploads';
  static const String _selectedFolderKey = 'selected_folder';
  static const String _recordingProgressKey = 'recording_progress';

  Future<void> saveRecordingProgress(RecordingProgress progress) async {
    final prefs = await SharedPreferences.getInstance();

    // ✅ FIXED: Extract filename from full path
    final fileName = path.basename(progress.path);

    final progressWithFileName = RecordingProgress(
      path: progress.path,
      fileName: fileName, // Store the filename separately
      duration: progress.duration,
      lastSaveTime: progress.lastSaveTime,
      isActive: progress.isActive,
      selectedFolder: progress.selectedFolder,
    );

    await prefs.setString(
      _recordingProgressKey,
      jsonEncode(progressWithFileName.toJson()),
    );
  }

  Future<RecordingProgress?> getRecordingProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString(_recordingProgressKey);

    if (progressJson == null) return null;

    try {
      final progressMap = jsonDecode(progressJson);
      final savedProgress = RecordingProgress.fromJson(progressMap);

      // Only return if recording was active
      if (!savedProgress.isActive) return null;

      // ✅ FIXED: Reconstruct the absolute path with current app container
      final directory = await getApplicationDocumentsDirectory();
      final currentAbsolutePath = path.join(
        directory.path,
        savedProgress.fileName,
      );

      // Check if the file actually exists at the new path
      final file = File(currentAbsolutePath);
      if (await file.exists()) {
        return RecordingProgress(
          path: currentAbsolutePath, // Use the reconstructed path
          fileName: savedProgress.fileName,
          duration: savedProgress.duration,
          lastSaveTime: savedProgress.lastSaveTime,
          isActive: savedProgress.isActive,
          selectedFolder: savedProgress.selectedFolder,
        );
      } else {
        print(
          'Recording file not found at reconstructed path: $currentAbsolutePath',
        );
        return null;
      }
    } catch (e) {
      print('Error parsing recording progress: $e');
      return null;
    }
  }

  Future<void> clearRecordingProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_recordingProgressKey);
  }

  Future<void> saveFailedUpload(String path) async {
    final prefs = await SharedPreferences.getInstance();
    final failedUploads = prefs.getStringList(_failedUploadsKey) ?? [];
    failedUploads.add(path);
    await prefs.setStringList(_failedUploadsKey, failedUploads);
  }

  Future<List<String>> getFailedUploads() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_failedUploadsKey) ?? [];
  }

  Future<void> removeFailedUpload(String path) async {
    final prefs = await SharedPreferences.getInstance();
    final failedUploads = prefs.getStringList(_failedUploadsKey) ?? [];
    failedUploads.remove(path);
    await prefs.setStringList(_failedUploadsKey, failedUploads);
  }

  Future<void> clearFailedUploads() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_failedUploadsKey);
  }

  Future<void> saveUploadMetadata({
    required String filePath,
    required String userId,
    required String? folderId,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final metadata = {
      'filePath': filePath,
      'userId': userId,
      'folderId': folderId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await prefs.setString('upload_metadata_${path.basename(filePath)}', jsonEncode(metadata));
  }

  Future<Map<String, dynamic>?> getUploadMetadata(String fileName) async {
    final prefs = await SharedPreferences.getInstance();
    final metadataJson = prefs.getString('upload_metadata_$fileName');

    if (metadataJson == null) return null;

    try {
      return jsonDecode(metadataJson);
    } catch (e) {
      return null;
    }
  }

  Future<void> removeUploadMetadata(String fileName) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('upload_metadata_$fileName');
  }
}
