import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';

class RecordingAudioHandler extends BaseAudioHandler {
  static const String _recordingId = 'recording_session';
  Timer? _updateTimer;
  Function()? _onStopCallback;
  
  // Current recording state
  Duration _recordingDuration = Duration.zero;
  bool _isRecording = false;

  @override
  Future<void> prepare() async {
    // Initialize the audio session for recording
    try {
      // Configure audio session for iOS
      final session = await AudioSession.instance;
      await session.configure(AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.allowBluetooth |
            AVAudioSessionCategoryOptions.defaultToSpeaker,
        avAudioSessionMode: AVAudioSessionMode.defaultMode,
        avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: false,
      ));

      await super.prepare();
      print('AudioServiceHandler prepared successfully with audio session');
    } catch (e) {
      print('AudioServiceHandler prepare failed: $e');
    }
  }

  /// Start the recording session with media notification
  Future<void> startRecording({
    required Function() onStopPressed,
  }) async {
    // Ensure the handler is prepared
    await prepare();

    // Activate the audio session for iOS
    try {
      final session = await AudioSession.instance;
      await session.setActive(true);
      print('AudioServiceHandler: Audio session activated');
    } catch (e) {
      print('AudioServiceHandler: Failed to activate audio session: $e');
    }

    _onStopCallback = onStopPressed;
    _isRecording = true;
    _recordingDuration = Duration.zero;

    // Create the media item for recording
    final mediaItem = MediaItem(
      id: _recordingId,
      title: '🔴 Recording Audio',
      artist: 'Sluqe',
      album: 'Recording Session',
      duration: Duration(hours: 24), // Set a long duration for live recording
      artUri: null,
      extras: {
        'isRecording': true,
        'recordingDuration': _recordingDuration.inSeconds,
      },
    );

    // Set the media item first
    this.mediaItem.add(mediaItem);
    print('AudioServiceHandler: Media item set - ${mediaItem.title}');

    // Set playback state to playing (recording) with proper iOS configuration
    final playbackStateObj = PlaybackState(
      controls: [
        MediaControl.stop,
      ],
      systemActions: {
        MediaAction.stop,
      },
      playing: true,
      processingState: AudioProcessingState.ready,
      updatePosition: _recordingDuration,
      bufferedPosition: _recordingDuration,
      speed: 1.0,
    );

    playbackState.add(playbackStateObj);
    print('AudioServiceHandler: Playback state set - playing: ${playbackStateObj.playing}');

    // Start updating the recording time
    _startDurationUpdates();

    print('AudioServiceHandler: Recording session started successfully');
  }

  /// Update the recording duration
  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    // Update the media item with new duration and formatted time in title
    final currentItem = mediaItem.value;
    if (currentItem != null) {
      final formattedDuration = formatDuration(duration);
      final updatedItem = currentItem.copyWith(
        title: '🔴 Recording Audio - $formattedDuration',
        extras: {
          ...currentItem.extras ?? {},
          'recordingDuration': duration.inSeconds,
        },
      );
      mediaItem.add(updatedItem);
    }

    // Update playback state with new position
    playbackState.add(PlaybackState(
      controls: [
        MediaControl.stop,
      ],
      systemActions: {
        MediaAction.stop,
      },
      playing: true,
      processingState: AudioProcessingState.ready,
      updatePosition: duration,
      bufferedPosition: duration,
      speed: 1.0,
    ));
  }

  /// Stop the recording session
  Future<void> stopRecording() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    // Deactivate the audio session
    try {
      final session = await AudioSession.instance;
      await session.setActive(false);
      print('AudioServiceHandler: Audio session deactivated');
    } catch (e) {
      print('AudioServiceHandler: Failed to deactivate audio session: $e');
    }

    // Update playback state to stopped
    playbackState.add(PlaybackState(
      controls: [],
      systemActions: {},
      playing: false,
      processingState: AudioProcessingState.idle,
      updatePosition: _recordingDuration,
    ));

    // Clear the media item
    mediaItem.add(null);
  }

  @override
  Future<void> stop() async {
    await stopRecording();
    _onStopCallback?.call();
    await super.stop();
  }

  @override
  Future<void> pause() async {
    // For recording, pause means stop
    await stop();
  }

  void _startDurationUpdates() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }
      
      // Duration will be updated externally via updateRecordingDuration
      // This timer is just a fallback to ensure regular updates
    });
  }

  @override
  Future<void> onTaskRemoved() async {
    // Handle when the app is swiped away
    await stop();
    await super.onTaskRemoved();
  }

  @override
  Future<void> onNotificationDeleted() async {
    // Handle when notification is dismissed
    await stop();
    await super.onNotificationDeleted();
  }

  /// Format duration for display
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  /// Check if currently recording
  bool get isRecording => _isRecording;
  
  /// Get current recording duration
  Duration get recordingDuration => _recordingDuration;
}
