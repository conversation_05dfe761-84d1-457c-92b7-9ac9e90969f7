import 'dart:async';
import 'dart:io';

class NetworkService {
  static NetworkService? _instance;
  static NetworkService get instance => _instance ??= NetworkService._();
  
  NetworkService._();
  
  StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  Stream<bool> get connectivityStream => _connectivityController.stream;
  
  bool _isConnected = true;
  bool get isConnected => _isConnected;
  
  Timer? _connectivityTimer;

  void startMonitoring() {
    _connectivityTimer?.cancel();
    _connectivityTimer = Timer.periodic(Duration(seconds: 5), (timer) async {
      await _checkConnectivity();
    });
    
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      final wasConnected = _isConnected;
      _isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      if (wasConnected != _isConnected) {
        _connectivityController.add(_isConnected);
      }
    } catch (e) {
      final wasConnected = _isConnected;
      _isConnected = false;
      
      if (wasConnected != _isConnected) {
        _connectivityController.add(_isConnected);
      }
    }
  }

  void stopMonitoring() {
    _connectivityTimer?.cancel();
  }

  void dispose() {
    _connectivityTimer?.cancel();
    _connectivityController.close();
  }
}
