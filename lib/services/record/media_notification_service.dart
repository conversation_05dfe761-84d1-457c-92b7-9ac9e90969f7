import 'dart:async';
import 'package:sluqe/services/record/audio_service_manager.dart';

class MediaNotificationService {
  static MediaNotificationService? _instance;
  static MediaNotificationService get instance => _instance ??= MediaNotificationService._();

  MediaNotificationService._();

  /// Initialize the audio service
  Future<void> initialize() async {
    print('MediaNotificationService: Initializing...');
    await AudioServiceManager.instance.initialize();
    print('MediaNotificationService: Initialization complete');
  }

  /// Start recording notification
  Future<void> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    final success = await AudioServiceManager.instance.startRecordingNotification(
      onStopPressed: onStopPressed,
    );

    if (!success) {
      print('MediaNotificationService: Failed to start recording notification');
    }
  }

  /// Update recording duration in notification
  void updateRecordingDuration(Duration duration) {
    AudioServiceManager.instance.updateRecordingDuration(duration);
  }

  /// Stop recording notification
  Future<void> stopRecordingNotification() async {
    await AudioServiceManager.instance.stopRecordingNotification();
  }

  /// Check if recording notification is active
  bool get isRecording => AudioServiceManager.instance.isRecording;

  /// Get current recording duration
  Duration get recordingDuration => AudioServiceManager.instance.recordingDuration;

  /// Format duration for display
  String formatDuration(Duration duration) {
    return AudioServiceManager.instance.formatDuration(duration);
  }

  /// Dispose resources
  Future<void> dispose() async {
    await AudioServiceManager.instance.stopRecordingNotification();
  }

  /// Check if the service is available (iOS specific checks)
  bool get isAvailable => AudioServiceManager.instance.isAvailable;

  /// Check if service is initialized
  bool get isInitialized => AudioServiceManager.instance.isInitialized;
}
