import 'dart:io';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/level.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:path/path.dart' as path;

import 'package:path_provider/path_provider.dart';

class RecordingService {
  late RecorderController _recorderController;

  RecorderController get controller => _recorderController;
  String? _existingRecordingPath;
  String? _tempRecordingPath;
  void initialize() {
    _recorderController =
        RecorderController()
          ..androidEncoder = AndroidEncoder.aac
          ..androidOutputFormat = AndroidOutputFormat.mpeg4
          ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
          ..sampleRate = 44100;
  }

  Future<String> startRecording() async {
    final directory = await getApplicationDocumentsDirectory();
    final path =
        "${directory.path}/recordings_${DateTime.now().millisecondsSinceEpoch}.m4a";
    await _recorderController.record(path: path);
    return path;
  }

  Future<String?> stopRecording() async {
    final stoppedPath = await _recorderController.stop();

    if (_existingRecordingPath != null && _tempRecordingPath != null) {
      try {
        final existingFile = File(_existingRecordingPath!);
        final tempFile = File(_tempRecordingPath!);

        if (await existingFile.exists() && await tempFile.exists()) {
          print('Merging: ${_existingRecordingPath!} + ${_tempRecordingPath!}');

          final mergedPath = await _mergeAudioFilesWithFFmpeg(
            _existingRecordingPath!,
            _tempRecordingPath!,
          );

          if (await tempFile.exists()) {
            await tempFile.delete();
          }

          _existingRecordingPath = null;
          _tempRecordingPath = null;

          return mergedPath;
        } else {
          print('One of the files does not exist for merging');
          final fallbackPath = _existingRecordingPath ?? stoppedPath;
          _existingRecordingPath = null;
          _tempRecordingPath = null;
          return fallbackPath;
        }
      } catch (e) {
        final fallbackPath = _existingRecordingPath ?? stoppedPath;

        _existingRecordingPath = null;
        _tempRecordingPath = null;

        return fallbackPath;
      }
    }

    return stoppedPath;
  }

  Future<String> _mergeAudioFilesWithFFmpeg(
    String existingPath,
    String newPath,
  ) async {
    final directory = await getApplicationDocumentsDirectory();
    final outputPath =
        "${directory.path}/merged_${DateTime.now().millisecondsSinceEpoch}.m4a";

    try {
      final existingFile = File(existingPath);
      final newFile = File(newPath);

      if (!await existingFile.exists()) {
        return newPath;
      }

      if (!await newFile.exists()) {
        return existingPath;
      }

      final command =
          '-y -i "$existingPath" -i "$newPath" '
          '-filter_complex "[0:a:0][1:a:0]concat=n=2:v=0:a=1[outa]" '
          '-map "[outa]" -c:a aac -b:a 64k -ac 1 -ar 44100 "$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        final outputFile = File(outputPath);
        if (await outputFile.exists()) {
          final fileSize = await outputFile.length();
          print('Merged file size: $fileSize bytes');

          if (fileSize > 0) {
            if (await existingFile.exists()) {
              await existingFile.delete();
            }
            if (await newFile.exists()) {
              await newFile.delete();
            }

            return outputPath;
          } else {
            throw Exception('Merged file is empty');
          }
        } else {
          throw Exception('Merged file was not created');
        }
      } else {
        final logs = await session.getAllLogs();
        final errorLogs = logs.where(
          (log) => log.getLevel() == Level.avLogError,
        );
        final errorMessage = errorLogs
            .map((log) => log.getMessage())
            .join('\n');

        print('FFmpeg detailed error: $errorMessage');
        throw Exception(
          'FFmpeg failed with code: $returnCode, Error: $errorMessage',
        );
      }
    } catch (e) {
      print('Merge error: $e');
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }
      rethrow;
    }
  }

  Future<String> resumeRecording(String existingPath) async {
    final directory = await getApplicationDocumentsDirectory();
    final tempPath =
        "${directory.path}/temp_${DateTime.now().millisecondsSinceEpoch}.m4a";

    final existingFile = File(existingPath);
    if (!await existingFile.exists()) {
      print('Warning: Existing recording file not found at $existingPath');

      final fileName = path.basename(existingPath);
      final reconstructedPath = path.join(directory.path, fileName);
      final reconstructedFile = File(reconstructedPath);

      if (await reconstructedFile.exists()) {
        print('Found recording at reconstructed path: $reconstructedPath');
        _existingRecordingPath = reconstructedPath;
        _tempRecordingPath = tempPath;
        await _recorderController.record(path: tempPath);
        return reconstructedPath;
      } else {
        print('Recording file completely lost, starting fresh');
        return await startRecording();
      }
    }

    print('Resuming recording - existing: $existingPath, temp: $tempPath');

    _existingRecordingPath = existingPath;
    _tempRecordingPath = tempPath;

    await _recorderController.record(path: tempPath);
    return existingPath;
  }

  void dispose() {
    _recorderController.dispose();
  }

  bool get hasPermission => _recorderController.hasPermission;
}
