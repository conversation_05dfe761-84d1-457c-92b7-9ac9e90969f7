import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/services/record/network_service.dart';
import 'package:sluqe/services/record/upload_service.dart';

class BackgroundUploadService {
  static const String _uploadQueueKey = 'upload_queue';
  static const String _activeUploadKey = 'active_upload';
  
  final UploadService _uploadService = UploadService();
  Timer? _uploadTimer;
  bool _isProcessing = false;
  StreamSubscription? _networkSubscription;

  Future<void> addToQueue(String filePath, String userId, Folder? folder) async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_uploadQueueKey) ?? '[]';
    final queue = List<Map<String, dynamic>>.from(jsonDecode(queueJson));
    
    final uploadItem = {
      'filePath': filePath,
      'userId': userId,
      'folder': folder?.toMap(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'retryCount': 0,
    };
    
    queue.add(uploadItem);
    await prefs.setString(_uploadQueueKey, jsonEncode(queue));
    
    _startProcessing();
  }

  Future<void> _startProcessing() async {
    if (_isProcessing) return;

    _isProcessing = true;
    _uploadTimer?.cancel();

    _networkSubscription?.cancel();
    _networkSubscription = NetworkService.instance.connectivityStream.listen((isConnected) {
      if (isConnected && _isProcessing) {
        _processQueue();
      }
    });

    NetworkService.instance.startMonitoring();

    _uploadTimer = Timer.periodic(Duration(seconds: 5), (timer) async {
      if (NetworkService.instance.isConnected) {
        await _processQueue();
      }
    });

    await _processQueue();
  }

  Future<void> _processQueue() async {
    if (!NetworkService.instance.isConnected) {
      return;
    }

    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_uploadQueueKey) ?? '[]';
    final queue = List<Map<String, dynamic>>.from(jsonDecode(queueJson));

    if (queue.isEmpty) {
      _stopProcessing();
      return;
    }

    final activeUpload = prefs.getString(_activeUploadKey);
    if (activeUpload != null) {
      return;
    }

    final uploadItem = queue.first;
    await prefs.setString(_activeUploadKey, jsonEncode(uploadItem));
    
    try {
      final filePath = uploadItem['filePath'] as String;
      final userId = uploadItem['userId'] as String;
      final folderMap = uploadItem['folder'] as Map<String, dynamic>?;
      final folder = folderMap != null ? Folder.fromMap(folderMap) : null;
      
      if (!await File(filePath).exists()) {
        throw Exception('File not found: $filePath');
      }
      
      await _uploadService.uploadRecording(filePath, userId, folder);
      
      queue.removeAt(0);
      await prefs.setString(_uploadQueueKey, jsonEncode(queue));
      await prefs.remove(_activeUploadKey);
      
      await File(filePath).delete();
      
    } catch (e) {
      uploadItem['retryCount'] = (uploadItem['retryCount'] as int) + 1;
      
      if (uploadItem['retryCount'] >= 3) {
        queue.removeAt(0);
      } else {
        queue[0] = uploadItem;
      }
      
      await prefs.setString(_uploadQueueKey, jsonEncode(queue));
      await prefs.remove(_activeUploadKey);
    }
  }

  void _stopProcessing() {
    _isProcessing = false;
    _uploadTimer?.cancel();
    _networkSubscription?.cancel();
    NetworkService.instance.stopMonitoring();
  }

  Future<void> resumeProcessing() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_activeUploadKey);
    _startProcessing();
  }

  Future<List<Map<String, dynamic>>> getQueueStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_uploadQueueKey) ?? '[]';
    return List<Map<String, dynamic>>.from(jsonDecode(queueJson));
  }

  Future<void> clearQueue() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_uploadQueueKey);
    await prefs.remove(_activeUploadKey);
    _stopProcessing();
  }

  void dispose() {
    _stopProcessing();
    NetworkService.instance.dispose();
  }
}
