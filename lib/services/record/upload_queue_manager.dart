import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/services/record/upload_service.dart';

class UploadQueueManager {
  static const String _queueKey = 'pending_uploads';
  static const String _processingKey = 'processing_upload';
  
  final UploadService _uploadService = UploadService();
  Timer? _processingTimer;
  bool _isProcessing = false;

  Future<void> addUpload({
    required String filePath,
    required String userId,
    required Folder? folder,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_queueKey) ?? '[]';
    final queue = List<Map<String, dynamic>>.from(jsonDecode(queueJson));
    
    final uploadData = {
      'filePath': filePath,
      'userId': userId,
      'folder': folder?.toMap(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'attempts': 0,
    };
    
    queue.add(uploadData);
    await prefs.setString(_queueKey, jsonEncode(queue));
    
    _startProcessing();
  }

  Future<void> _startProcessing() async {
    if (_isProcessing) return;
    
    _isProcessing = true;
    _processingTimer = Timer.periodic(Duration(seconds: 10), (timer) async {
      await _processNextUpload();
    });
    
    await _processNextUpload();
  }

  Future<void> _processNextUpload() async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_queueKey) ?? '[]';
    final queue = List<Map<String, dynamic>>.from(jsonDecode(queueJson));
    
    if (queue.isEmpty) {
      _stopProcessing();
      return;
    }
    
    final processingUpload = prefs.getString(_processingKey);
    if (processingUpload != null) {
      return;
    }
    
    final uploadData = queue.first;
    await prefs.setString(_processingKey, jsonEncode(uploadData));
    
    try {
      final filePath = uploadData['filePath'] as String;
      final userId = uploadData['userId'] as String;
      final folderMap = uploadData['folder'] as Map<String, dynamic>?;
      final folder = folderMap != null ? Folder.fromMap(folderMap) : null;
      
      if (!await File(filePath).exists()) {
        throw Exception('File not found');
      }
      
      await _uploadService.uploadRecording(filePath, userId, folder);
      
      queue.removeAt(0);
      await prefs.setString(_queueKey, jsonEncode(queue));
      await prefs.remove(_processingKey);
      
      try {
        await File(filePath).delete();
      } catch (e) {
        // File deletion failed, but upload succeeded
      }
      
    } catch (e) {
      uploadData['attempts'] = (uploadData['attempts'] as int) + 1;
      
      if (uploadData['attempts'] >= 5) {
        queue.removeAt(0);
      } else {
        queue[0] = uploadData;
      }
      
      await prefs.setString(_queueKey, jsonEncode(queue));
      await prefs.remove(_processingKey);
    }
  }

  void _stopProcessing() {
    _isProcessing = false;
    _processingTimer?.cancel();
  }

  Future<void> resumeProcessing() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_processingKey);
    
    if (!_isProcessing) {
      _startProcessing();
    }
  }

  Future<int> getQueueLength() async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_queueKey) ?? '[]';
    final queue = List<Map<String, dynamic>>.from(jsonDecode(queueJson));
    return queue.length;
  }

  Future<void> clearQueue() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_queueKey);
    await prefs.remove(_processingKey);
    _stopProcessing();
  }

  void dispose() {
    _stopProcessing();
  }
}
