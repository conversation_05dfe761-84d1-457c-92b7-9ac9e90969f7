import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sluqe/models/folder.dart';

class FoldersServices {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<Folder?> createFoler(String name, String userId) async {
    final folder = Folder(
      createdAt: DateTime.now(),
      path: name,
      updatedAt: DateTime.now(),
    );

    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('folders')
          .add(folder.toMap());
      print("Folder created successfully");
      return folder;
    } catch (e) {
      return null;
      print("Error creating folder: $e");
    }
  }

  Future<List<Folder>> getUserFolders(String userId) async {
    try {
      final snapshot =
          await _firestore
              .collection("users")
              .doc(userId)
              .collection('folders')
              .get();

      List<Folder> folders = [];
      for (QueryDocumentSnapshot doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['deletedAt'] != null) {
          continue;
        }
        Folder folder = Folder.fromMap(data);
        folder.id = doc.id;
        folders.add(folder);
      }
      return folders;
    } catch (e) {
      print("Error getting folders: $e");
      return [];
    }
  }

  Future<Folder> getFolderById(String folderId, String userId) async {
    try {
      DocumentSnapshot folderDoc =
          await _firestore
              .collection('users')
              .doc(userId)
              .collection('folders')
              .doc(folderId)
              .get();

      if (folderDoc.exists) {
        Map<String, dynamic> folderData =
            folderDoc.data() as Map<String, dynamic>;
        return Folder.fromMap(folderData);
      }

      return Folder(
        createdAt: DateTime.now(),
        path: 'root',
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      print('Error getting folder name: $e');
      return Folder(
        createdAt: DateTime.now(),
        path: 'root',
        updatedAt: DateTime.now(),
      );
    }
  }
}
