import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/blocs/authentication/authentication_bloc.dart';
import 'package:sluqe/blocs/folders/folders_bloc.dart';
import 'package:sluqe/blocs/record/record_bloc.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/services/record/url_handler_service.dart';
import 'package:sluqe/widgets/upload_progress_indicator.dart';

import '../../core/constants.dart';

class RecordingSheet extends StatefulWidget {
  const RecordingSheet({super.key});

  @override
  State<RecordingSheet> createState() => _RecordingSheetState();
}

class _RecordingSheetState extends State<RecordingSheet> {
  late RecordBloc _recordBloc;
  Folder? _selectedFolder;
  bool isSelected = false;

  @override
  void initState() {
    super.initState();
    _recordBloc = RecordBloc();

    // Initialize URL handler for Dynamic Island stop button
    UrlHandlerService().initialize(
      onStopRecording: () {
        if (_recordBloc.state.isRecording) {
          _recordBloc.add(StopRecording());
        }
      },
    );

    _initialize();
  }

  void _initialize() {
    final userId =
        (context.read<AuthenticationBloc>().state as Authenticated).user.uid;
    _recordBloc.add(InitializeRecording(userId));
  }

  void _restoreSelectedFolder(RecordState recordState) {
    // FIXED: Restore folder from record bloc if available
    if (recordState.hasRecoveredRecording &&
        _recordBloc.selectedFolder != null) {
      setState(() {
        _selectedFolder = _recordBloc.selectedFolder;
      });
    }
  }

  @override
  void dispose() {
    UrlHandlerService().dispose();
    _recordBloc.close();
    super.dispose();
  }

  final TextEditingController _folderNameController = TextEditingController();

  void _showRecoveryDialog(RecordState state) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Resume Recording?'),
            content: Text(
              'You have an unfinished recording of ${_recordBloc.formatDuration(state.recordingDuration)}. Do you want to resume?',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _recordBloc.add(DiscardRecording());
                },
                child: Text('Discard'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _recordBloc.add(ResumeRecording());
                },
                child: Text('Resume'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _recordBloc,
      child: BlocListener<RecordBloc, RecordState>(
        listener: (context, state) {
          if (state.hasRecoveredRecording) {
            _restoreSelectedFolder(state);
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showRecoveryDialog(state);
            });
          }

          if (state.isUploadSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text("Record uploaded successfully"),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state.isUploadFailed && !state.isUploading) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text("Upload failed - added to queue for retry"),
                backgroundColor: Colors.orange,
                action: SnackBarAction(
                  label: "Retry Now",
                  onPressed: () {
                    context.read<RecordBloc>().add(RetryFailedUploads());
                  },
                ),
              ),
            );
          }
        },
        child: BlocBuilder<RecordBloc, RecordState>(
          builder: (context, state) {
            return Container(
              decoration: BoxDecoration(
                color: primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              padding: EdgeInsets.all(15).copyWith(top: 30),
              height: 300,
              width: double.infinity,

              child: Column(
                spacing: 25,
                children: [
                  _buildFolderDropdown(context, state),
                  _buildRecordingStatus(context, state),
                  if (state.isUploading || state.isUploadFailed)
                    UploadProgressIndicator(
                      progress: state.uploadProgress,
                      isUploading: state.isUploading,
                      errorMessage: state.isUploadFailed ? state.uploadError : null,
                      onRetry: state.isUploadFailed ? () {
                        context.read<RecordBloc>().add(RetryFailedUploads());
                      } : null,
                    ),
                  _buildRecordingButton(context, state),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFolderDropdown(BuildContext context, RecordState state) {
    final userId =
        (context.read<AuthenticationBloc>().state as Authenticated).user.uid;

    return SizedBox(
      height: 40,
      width: MediaQuery.of(context).size.width * 0.7,
      child: BlocProvider(
        create: (context) => FoldersBloc()..add(LoadFolders(userId)),
        child: BlocConsumer<FoldersBloc, FoldersState>(
          listener: (context, state) {
            if (state is FoldersCreated) {
              setState(() {
                _selectedFolder = state.folder;
              });
            }
          },
          builder: (context, state) {
            List<DropdownMenuItem<dynamic>> items = [];

            if (state is FoldersLoaded) {
              items.addAll(
                state.folders
                    .map(
                      (folder) => DropdownMenuItem(
                        value: folder,
                        child: Text(folder.path),
                      ),
                    )
                    .toList(),
              );
            } else if (state is FoldersLoading) {
              items.add(
                DropdownMenuItem<dynamic>(
                  value: null,
                  child: Text("Loading..."),
                ),
              );
            } else if (state is FoldersCreated) {
              items.add(
                DropdownMenuItem<dynamic>(
                  value: _selectedFolder,
                  child: Text(_selectedFolder!.path),
                ),
              );
            }
            if (_selectedFolder != null &&
                state is! FoldersCreated &&
                isSelected == false) {
              items.add(
                DropdownMenuItem(
                  child: Text(_selectedFolder!.path),
                  value: _selectedFolder,
                ),
              );
            }

            items.add(
              DropdownMenuItem<String>(
                value: "create_new",
                child: Row(
                  children: [
                    Icon(Icons.add, size: 16, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      "Create New Folder",
                      style: TextStyle(color: Colors.blue),
                    ),
                  ],
                ),
              ),
            );
            return DropdownButtonFormField<dynamic>(
              value: _selectedFolder,
              padding: EdgeInsets.symmetric(horizontal: 10),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.zero,
                hintStyle: TextStyle(color: Colors.black, fontSize: 10),
                hintText: "Select Folder",
                border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.white,
              ),

              items: items,
              onChanged: (folder) {
                if (folder == "create_new") {
                  _showCreateFolderDialog(
                    context,
                    _folderNameController,
                    userId,
                    state,
                  );
                } else if (folder is Folder) {
                  _selectedFolder = folder;
                  isSelected = true;
                  setState(() {});
                }
              },
            );
          },
        ),
      ),
    );
  }

  void _showCreateFolderDialog(
    BuildContext context,
    TextEditingController controller,
    String userId,
    FoldersState state,
  ) {
    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text('Create New Folder'),
            content: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Enter folder name',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  controller.clear();
                },
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  final folderName = controller.text.trim();
                  if (folderName.isNotEmpty) {
                    // Replace 'your_user_id_here' with actual user ID
                    context.read<FoldersBloc>().add(
                      CreateFolder(folderName, userId),
                    );

                    Navigator.of(dialogContext).pop();
                    controller.clear();
                  }
                },
                child: Text('Create'),
              ),
            ],
          ),
    );
  }

  Widget _buildRecordingStatus(BuildContext context, RecordState state) {
    return Row(
      spacing: 10,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CircleAvatar(
          backgroundColor: state.isRecording ? Colors.red : Colors.green,
          radius: 5,
        ),
        Text(
          "${state.isRecording ? "REC" : "READY"} ${_recordBloc.formatDuration(state.recordingDuration)}",
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        Expanded(
          child: SizedBox(
            height: 60,

            child:
                state.isRecording
                    ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: AudioWaveforms(
                        waveStyle: WaveStyle(
                          waveColor: Colors.white.withOpacity(0.3),
                          scaleFactor: 50,
                          waveThickness: 3,

                          waveCap: StrokeCap.round,
                          extendWaveform: true,
                          showMiddleLine: false,
                        ),
                        size: Size(MediaQuery.of(context).size.width - 100, 60),
                        recorderController: _recordBloc.recorderController,
                      ),
                    )
                    : Container(
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          40,
                          (index) => Container(
                            width: 3,
                            height: 10,
                            margin: const EdgeInsets.symmetric(horizontal: 1),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      ),
                    ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecordingButton(BuildContext context, RecordState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: _getButtonColor(state),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onPressed: _getButtonEnabled(state) ? () async {
            if (state.isRecording) {
              context.read<RecordBloc>().add(StopRecording());
            } else if (state.isUploadFailed) {
              context.read<RecordBloc>().add(RetryFailedUploads());
            } else {
              context.read<RecordBloc>().add(StartRecording(_selectedFolder));
            }
            setState(() {});
          } : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            width: MediaQuery.of(context).size.width * 0.7,
            child: Row(
              spacing: 10,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (state.isUploading)
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      value: state.uploadProgress > 0 ? state.uploadProgress : null,
                    ),
                  )
                else
                  Icon(_getButtonIcon(state)),
                Text(_getButtonText(state)),
                if (state.isUploading && state.uploadProgress > 0)
                  Text("${(state.uploadProgress * 100).toInt()}%"),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getButtonColor(RecordState state) {
    if (state.isUploading) return Colors.orange;
    if (state.isUploadFailed) return Colors.red.shade700;
    if (state.isRecording) return Colors.red;
    return Colors.green;
  }

  IconData _getButtonIcon(RecordState state) {
    if (state.isUploadFailed) return Icons.refresh;
    if (state.isRecording) return Icons.stop_circle_outlined;
    return Icons.play_arrow_outlined;
  }

  String _getButtonText(RecordState state) {
    if (state.isUploading) return "Uploading...";
    if (state.isUploadFailed) return "Retry Upload";
    if (state.isRecording) return "Stop Recording";
    return "Start Recording";
  }

  bool _getButtonEnabled(RecordState state) {
    return !state.isUploading;
  }
}
