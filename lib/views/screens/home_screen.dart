import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sluqe/core/constants.dart';
import 'package:sluqe/models/audio.dart';
import 'package:sluqe/views/screens/audio_details_screen.dart';
import 'package:sluqe/views/screens/audios_screen.dart';
import '../widgets/recording_sheet.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _index = 0;
  Audio? _selectedAudio;

  void _onTabItemSelected(int index, BuildContext context) {
    if (index != 2) {
      setState(() {
        _index = index;
      });
    } else {
      _showRecordSheet(context);
    }
  }

  void _showRecordSheet(BuildContext context) {
    showModalBottomSheet(
      clipBehavior: Clip.antiAlias,
      context: context,
      builder: (context) {
        return RecordingSheet();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> screens = [
      AudioListScreen(
        onAudioSelected: (audio) {
          setState(() {
            _selectedAudio = audio;
            _index = 2;
          });
        },
      ),

      const Center(child: Text('Chat')),
      _selectedAudio != null
          ? AudioDetailsScreen(audio: _selectedAudio!)
          : Text(""),
    ];

    return Scaffold(
      backgroundColor: bgColor,
      body: screens[_index],
      bottomNavigationBar: BottomAppBar(
        padding: EdgeInsets.symmetric(horizontal: 16),
        color: bgColor,
        height: 65,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Divider(color: Colors.grey.shade500),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  child: Column(
                    children: [
                      Icon(
                        size: 20,
                        FontAwesomeIcons.house,
                        color: _index == 0 ? primaryColor : Colors.grey,
                      ),
                      Text(
                        "Home",
                        style: TextStyle(
                          color: _index == 0 ? primaryColor : Colors.grey,
                          fontWeight:
                              _index == 0 ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    _onTabItemSelected(0, context);
                  },
                ),
                GestureDetector(
                  child: Column(
                    children: [
                      Icon(
                        size: 20,

                        Icons.assistant_rounded,
                        color: _index == 1 ? primaryColor : Colors.grey,
                      ),
                      Text(
                        "Ask Assistant",
                        style: TextStyle(
                          color: _index == 1 ? primaryColor : Colors.grey,
                          fontWeight:
                              _index == 1 ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    _onTabItemSelected(1, context);
                  },
                ),

                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () {
                    _onTabItemSelected(2, context);
                  },
                  child: Row(
                    children: [
                      Icon(Icons.mic, color: Colors.white),
                      Text(
                        "Record Audio",
                        style: TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
