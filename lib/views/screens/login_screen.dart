import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/blocs/authentication/authentication_bloc.dart';
import 'package:sluqe/core/constants.dart';
import 'package:sluqe/views/screens/home_screen.dart';

// Custom painter to draw gray circular section over orange background
class GrayCirclePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = bgColor
          ..style = PaintingStyle.fill;

    // Create gray circle positioned to match reference exactly
    // Gray section should be at top, so position circle above screen
    final center = Offset(
      size.width * -0.2, // Move slightly right to fix left alignment
      -size.height * 0.17, // Position above screen to create top gray section
    );

    final radius = size.height; // Right size for the curve

    canvas.drawCircle(center, radius, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF47F6B), // Orange background
      body: BlocConsumer<AuthenticationBloc, AuthenticationState>(
        listener: (context, state) {
          if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is Authenticated) {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => HomeScreen()),
              (route) => false,
            );
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              // Gray circular section that creates the curved boundary
              Positioned.fill(child: CustomPaint(painter: GrayCirclePainter())),

              // Content positioned to match reference exactly
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Top section with stars positioned diagonally like in reference
                      Column(
                        children: [
                          SizedBox(height: 40),
                          SizedBox(
                            height: 100,
                            child: Stack(
                              children: [
                                // First star (top, pushed more left)
                                Positioned(
                                  top: 20,
                                  right: 90,
                                  child: Image.asset(
                                    "assets/ai.png",
                                    width: 20,
                                    scale: 1.2,
                                  ),
                                ),
                                // Second star (middle, bigger and more left)
                                Positioned(
                                  top: 55,
                                  right: 110,
                                  child: Image.asset(
                                    "assets/ai.png",
                                    width: 22,
                                    scale: 0.1,
                                  ),
                                ),
                                // Third star (bottom, pushed left)
                                Positioned(
                                  top: 50,
                                  right: 70,
                                  child: Image.asset(
                                    "assets/ai.png",
                                    width: 20,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Main title positioned like in reference
                          const Text(
                            "World's #1\nAI Voice Note Agent",
                            style: TextStyle(
                              fontSize: 23,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFFF47F6B),
                              height: 1.2,
                            ),
                          ),
                          const SizedBox(height: 150),

                          Column(
                            children: [
                              // Subtitle positioned like in reference
                              const Text(
                                "Join Thousands of CEOs, Creators, and Thinkers Who Use Sluqe to Organize Their Thoughts",
                                style: TextStyle(
                                  fontSize: 15,
                                  color: primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),

                          // Push avatars to bottom of screen like in reference

                          // Profile images positioned closer together and pushed higher
                          Container(
                            height: 150,
                            child: Stack(
                              children: [
                                // First profile image (positioned center-right and higher)
                                Positioned(
                                  right: 50,
                                  top: 0, // Make right avatar higher
                                  child: Container(
                                    width: 70,
                                    height: 70,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey[300],
                                    ),
                                    child: ClipOval(
                                      child: Image.asset(
                                        "assets/Avatar2.png",
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),

                                // Second profile image (positioned closer and lower)
                                Positioned(
                                  left: 150,
                                  bottom:
                                      10, // Push up but keep lower than right one
                                  child: Container(
                                    width: 70,
                                    height: 70,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey[300],
                                    ),
                                    child: ClipOval(
                                      child: Image.asset(
                                        "assets/Avatar.png",
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Center(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onPressed: () {
                              context.read<AuthenticationBloc>().add(
                                AuthSignIn(),
                              );
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 15),
                              child: Row(
                                spacing: 10,
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  Image.asset("assets/google.png", width: 20),
                                  Text(
                                    "Continue With Google",
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
