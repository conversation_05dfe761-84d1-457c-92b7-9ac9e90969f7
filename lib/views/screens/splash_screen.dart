import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/blocs/authentication/authentication_bloc.dart';
import 'package:sluqe/core/constants.dart';
import 'package:sluqe/views/screens/home_screen.dart';
import 'package:sluqe/views/screens/login_screen.dart';
import 'package:sluqe/views/screens/login_screen_2.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (_context, state) {
        print(state.runtimeType);
        if (state is AuthenticationInitial) {
        } else if (state is Authenticated) {
          Future.delayed(Duration(seconds: 2), () {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => HomeScreen()),
              (route) => false,
            );
          });
        } else if (state is Unauthenticated) {
          Future.delayed(Duration(seconds: 2), () {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => LoginScreen()),
              (route) => false,
            );
          });
        }
      },

      child: Scaffold(
        backgroundColor: primaryColor,
        body: Center(child: Image.asset("assets/splash_logo.png")),
      ),
    );
  }
}
