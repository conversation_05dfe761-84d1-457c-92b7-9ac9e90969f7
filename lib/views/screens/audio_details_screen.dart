import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:sluqe/blocs/transcription/transcription_bloc.dart';
import 'package:sluqe/core/constants.dart';
import 'package:sluqe/models/audio.dart';

class AudioDetailsScreen extends StatefulWidget {
  final Audio audio;

  const AudioDetailsScreen({super.key, required this.audio});

  @override
  State<AudioDetailsScreen> createState() => _AudioDetailsScreenState();
}

class _AudioDetailsScreenState extends State<AudioDetailsScreen> {
  late TranscriptionBloc _audioPlayerBloc;

  @override
  void initState() {
    super.initState();
    _audioPlayerBloc = TranscriptionBloc();
    _audioPlayerBloc.add(InitializeAudioPlayer(widget.audio));
  }

  @override
  void dispose() {
    _audioPlayerBloc.add(DisposePlayer());
    _audioPlayerBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _audioPlayerBloc,
      child: Scaffold(
        backgroundColor: bgColor,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAudioPlayer(),
                  const SizedBox(height: 10),
                  const Divider(),
                  const SizedBox(height: 24),
                  _buildHeader(),
                  const SizedBox(height: 32),
                  _buildTranscriptSection(),
                  const SizedBox(height: 20),
                  _buildNoteSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  //sluqeinc
  Widget _buildAudioPlayer() {
    return BlocBuilder<TranscriptionBloc, TranscriptionState>(
      bloc: _audioPlayerBloc, // Use the specific bloc instance
      builder: (context, state) {
        if (state.isLoading) {
          return SizedBox(
            height: 50,
            child: Center(
              child: LoadingAnimationWidget.stretchedDots(
                color: Colors.black,
                size: 30,
              ),
            ),
          );
        }

        if (state.errorMessage != null) {
          return Container(
            height: 50,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                'Error: ${state.errorMessage}',
                style: TextStyle(color: Colors.red.shade700),
              ),
            ),
          );
        }

        return Row(
          children: [
            _buildPlayPauseButton(state),
            const SizedBox(width: 12),
            _buildWaveform(state),
            const SizedBox(width: 12),
            _buildDurationText(state),
          ],
        );
      },
    );
  }

  Widget _buildPlayPauseButton(TranscriptionState state) {
    return GestureDetector(
      onTap:
          state.isInitialized
              ? () {
                if (state.isPlaying) {
                  _audioPlayerBloc.add(PauseAudio());
                } else {
                  _audioPlayerBloc.add(PlayAudio());
                }
              }
              : null,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: state.isInitialized ? const Color(0xFF333333) : Colors.grey,
        ),
        child: Icon(
          state.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
          color: Colors.white,
          size: 36,
        ),
      ),
    );
  }

  Widget _buildWaveform(TranscriptionState state) {
    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          if (!state.isInitialized || !state.isWaveformReady) {
            return Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }

          final style = PlayerWaveStyle(
            fixedWaveColor: Colors.grey,
            liveWaveColor: const Color(0xFF000000),
            showSeekLine: false,
            waveThickness: 2.0,
          );

          return AudioFileWaveforms(
            waveformType: WaveformType.fitWidth,
            size: Size(constraints.maxWidth, 50.0),
            playerController: _audioPlayerBloc.playerController,
            continuousWaveform: true,
            playerWaveStyle: style,
            enableSeekGesture: true,
          );
        },
      ),
    );
  }

  Widget _buildDurationText(TranscriptionState state) {
    final duration =
        state.totalDuration.inSeconds > 0
            ? state.totalDuration
            : Duration(seconds: widget.audio.duration);

    return Text(
      _audioPlayerBloc.audioService.formatDuration(duration),
      style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.audio.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Text(
              "${DateFormat('MMM d').format(widget.audio.createdAt)} • ${DateFormat.jm().format(widget.audio.createdAt)}",
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
            const SizedBox(width: 20),
            if (widget.audio.folderName != null) ...[
              Icon(
                Icons.folder_open_outlined,
                color: Colors.grey.shade700,
                size: 20,
              ),
              const SizedBox(width: 6),
              Flexible(
                flex: 2,
                child: Text(
                  widget.audio.folderName!,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 20),
            ],
            Icon(Icons.person_outline, color: Colors.grey.shade700, size: 20),
            const SizedBox(width: 6),
            Flexible(
              flex: 2,
              child: Text(
                widget.audio.speakers.join(', '),
                style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTranscriptSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Transcript",
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.bold,
            color: primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildSearchField(),
        const SizedBox(height: 16),
        _buildTranscriptList(),
      ],
    );
  }

  Widget _buildSearchField() {
    return BlocBuilder<TranscriptionBloc, TranscriptionState>(
      bloc: _audioPlayerBloc, // Use the specific bloc instance
      builder: (context, state) {
        return SizedBox(
          height: 35,
          child: TextField(
            onChanged: (value) {
              _audioPlayerBloc.add(SearchTranscript(value));
            },
            decoration: InputDecoration(
              hintText: "Search in Transcript",
              hintStyle: TextStyle(color: Colors.grey.shade600),
              suffixIcon: Icon(Icons.search, color: Colors.grey.shade600),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 20,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: const BorderSide(color: primaryColor),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTranscriptList() {
    return BlocBuilder<TranscriptionBloc, TranscriptionState>(
      bloc: _audioPlayerBloc, // Use the specific bloc instance
      builder: (context, state) {
        return Column(
          children:
              state.filteredTranscriptions
                  .map(
                    (transcript) =>
                        _buildTranscriptItem(transcript, state.searchQuery),
                  )
                  .toList(),
        );
      },
    );
  }

  Widget _buildTranscriptItem(Transcription transcript, String searchQuery) {
    final duration = Duration(
      milliseconds: (transcript.timestamp * 1000).round(),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: GestureDetector(
        onTap: () {
          _audioPlayerBloc.add(JumpToTranscript(transcript));
        },
        child: RichText(
          text: TextSpan(
            text: _audioPlayerBloc.audioService.formatDuration(duration),
            style: const TextStyle(
              color: Color(0xFFD95500),
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
            children: [
              TextSpan(
                children: _buildHighlightedText(transcript.text, searchQuery),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TextSpan> _buildHighlightedText(String text, String searchQuery) {
    if (searchQuery.isEmpty) {
      return [
        TextSpan(
          text: text,
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 15,
            height: 1.4,
          ),
        ),
      ];
    }

    final spans = <TextSpan>[];
    final lowerText = text.toLowerCase();
    final lowerQuery = searchQuery.toLowerCase();

    int start = 0;
    while (start < text.length) {
      final index = lowerText.indexOf(lowerQuery, start);
      if (index == -1) {
        spans.add(
          TextSpan(
            text: text.substring(start),
            style: const TextStyle(
              color: Color(0xFF333333),
              fontSize: 15,
              height: 1.4,
            ),
          ),
        );
        break;
      }

      if (index > start) {
        spans.add(
          TextSpan(
            text: text.substring(start, index),
            style: const TextStyle(
              color: Color(0xFF333333),
              fontSize: 15,
              height: 1.4,
            ),
          ),
        );
      }

      spans.add(
        TextSpan(
          text: text.substring(index, index + searchQuery.length),
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 15,
            height: 1.4,
            backgroundColor: Colors.yellow,
            fontWeight: FontWeight.bold,
          ),
        ),
      );

      start = index + searchQuery.length;
    }

    return spans;
  }

  Widget _buildNoteSection() {
    final lines = widget.audio.richText.split('\n');
    final List<Widget> children = [];

    for (var line in lines) {
      line = line.trim();
      if (line.startsWith('## ')) {
        children.add(
          Padding(
            padding: const EdgeInsets.only(top: 24.0, bottom: 8.0),
            child: Text(
              line.substring(3),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 15,
                color: Color(0xFF333333),
              ),
            ),
          ),
        );
      } else if (line.startsWith('- ')) {
        children.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "• ",
                  style: TextStyle(fontSize: 16, color: Color(0xFF333333)),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    line.substring(2),
                    style: const TextStyle(
                      fontSize: 15,
                      color: Color(0xFF333333),
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (line.isNotEmpty) {
        children.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              line,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF333333),
                height: 1.5,
              ),
            ),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Note",
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.bold,
            color: primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }
}
