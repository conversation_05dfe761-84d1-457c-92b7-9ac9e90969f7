import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class Folder {
  String? id;
  final DateTime createdAt;
  final String path;
  final DateTime updatedAt;

  Folder({
    required this.createdAt,
    required this.path,
    required this.updatedAt,
    this.id,
  });

  factory Folder.fromMap(Map<String, dynamic> map) {
    return Folder(
      createdAt: _parseDateTime(map['createdAt']),
      path: map['path'],
      updatedAt: _parseDateTime(map['updatedAt']),
    );
  }

  // Helper method to handle both Timestamp and String datetime formats
  static DateTime _parseDateTime(dynamic dateTime) {
    if (dateTime is Timestamp) {
      return dateTime.toDate();
    } else if (dateTime is String) {
      return DateTime.parse(dateTime);
    } else if (dateTime is DateTime) {
      return dateTime;
    } else {
      throw ArgumentError('Invalid datetime format: $dateTime');
    }
  }

  factory Folder.fromJson(dynamic json) {
    // Handle both String and Map input
    Map<String, dynamic> data;
    if (json is String) {
      data = jsonDecode(json);
    } else {
      data = json as Map<String, dynamic>;
    }

    return Folder(
      id: data['id'],
      path: data['path'],
      createdAt: DateTime.parse(data['createdAt']), // Parse String to DateTime
      updatedAt: DateTime.parse(data['updatedAt']), // Parse String to DateTime
    );
  }

  Map<String, dynamic> toMap() => {
    'id': id,
    'path': path,
    'createdAt': createdAt.toIso8601String(), // Convert DateTime to String
    'updatedAt': updatedAt.toIso8601String(),
  };

  String toJson() => jsonEncode(toMap());

  factory Folder.fromJsonString(String json) =>
      Folder.fromMap(jsonDecode(json));
}
