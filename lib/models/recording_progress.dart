import 'package:sluqe/models/folder.dart';

class RecordingProgress {
  final String fileName; // ✅ FIXED: Store just the filename
  final String path;
  final int duration;
  final int lastSaveTime;
  final bool isActive;
  final Folder? selectedFolder;

  const RecordingProgress({
    required this.path,
    required this.duration,
    required this.lastSaveTime,
    required this.isActive,
    this.selectedFolder,
    required this.fileName,
  });
  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'duration': duration,
      'lastSaveTime': lastSaveTime,
      'isActive': isActive,
      'selectedFolder': selectedFolder?.toMap(),
      'fileName': fileName,
    };
  }

  // Add fromJson method if not exists
  factory RecordingProgress.fromJson(Map<String, dynamic> json) {
    return RecordingProgress(
      fileName: json['fileName'] ?? '',
      path: json['path'],
      duration: json['duration'],
      lastSaveTime: json['lastSaveTime'],
      isActive: json['isActive'],
      selectedFolder:
          json['selectedFolder'] != null
              ? Folder.fromJson(json['selectedFolder'])
              : null,
    );
  }
}
