import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:shared_preferences/shared_preferences.dart';

@pragma('vm:entry-point')
void startCallback() {
  FlutterForegroundTask.setTaskHandler(MyTaskHandler());
}

class MyTaskHandler extends TaskHandler {
  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    print('Background task destroyed'); // Correct message
    await _saveRecordingState();
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    _saveRecordingProgress();
    // Don't update notification here - it's handled by the main app timer
  }



  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    print('Background task started');
  }

  @override
  void onNotificationButtonPressed(String id) {
    super.onNotificationButtonPressed(id);

    if (id == 'stop') {
      FlutterForegroundTask.sendDataToTask({'action': 'stop'});
    }
  }

  @override
  void onReceiveData(Object data) {
    super.onReceiveData(data);
    if (data is Map<String, dynamic>) {
      final action = data['action'];
      if (action == 'save_progress') {
        _saveRecordingProgress();
      } else if (action == 'stop') {
        _stopRecordingFromBackground();
      }
    }
  }

  Future<void> _saveRecordingProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt('last_save_time', currentTime);
      print('Progress saved at: $currentTime');
    } catch (e) {
      print('Error saving progress in background: $e');
    }
  }

  Future<void> _saveRecordingState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('recording_interrupted', true);
      await prefs.setInt(
        'interruption_time',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      print('Error saving recording state: $e');
    }
  }

  void _stopRecordingFromBackground() {
    // This would communicate back to your main app
    FlutterForegroundTask.sendDataToMain({'action': 'stop_recording'});
  }
}
