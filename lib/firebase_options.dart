// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform, kDebugMode;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }

    // Determine if this is a production build (App Store/Play Store)
    final bool isProduction = _isProductionBuild();

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return isProduction ? androidProd : android;
      case TargetPlatform.iOS:
        return isProduction ? iosProd : ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static bool _isProductionBuild() {
    if (kDebugMode) {
      return false; // Debug builds are never production
    }

    const String firebaseEnv = String.fromEnvironment('FIREBASE_ENV', defaultValue: '');
    if (firebaseEnv.isNotEmpty) {
      return firebaseEnv.toLowerCase() == 'prod' || firebaseEnv.toLowerCase() == 'production';
    }


    return true;
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBYeXTUl99LiCih_uPYZmCTsTAgg-VhZe0',
    appId: '1:1051414371573:android:221ba76bf3d152617039bc',
    messagingSenderId: '1051414371573',
    projectId: 'sluqe-qa',
    storageBucket: 'sluqe-qa.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCerj8f8-ctWMNfb8Xs4p6XCaPdLd3TgLs',
    appId: '1:1051414371573:ios:0a87abb275adbeee7039bc',
    messagingSenderId: '1051414371573',
    projectId: 'sluqe-qa',
    storageBucket: 'sluqe-qa.firebasestorage.app',
    iosClientId: '1051414371573-t78lf64heni6pda136emjuogbpce4r4v.apps.googleusercontent.com',
    iosBundleId: 'io.boet.sluqe',
  );

  
  static const FirebaseOptions androidProd = FirebaseOptions(
    apiKey: 'AIzaSyDcfRIy8_ZDqLNwGzasFVm31U3xNBg0i90',
    appId: '1:346766260563:android:0b8edee61d03d0e01d89a6',
    messagingSenderId: '346766260563',
    projectId: 'sluqe-prod',
    storageBucket: 'sluqe-prod.firebasestorage.app',
  );

  static const FirebaseOptions iosProd =FirebaseOptions(
    apiKey: 'AIzaSyATpLQe2xGUua6cRnwruzhH5QoiW5MzrEo',
    appId: '1:346766260563:ios:d8ae59dbaa2a74f31d89a6',
    messagingSenderId: '346766260563',
    projectId: 'sluqe-prod',
    storageBucket: 'sluqe-prod.firebasestorage.app',
    iosClientId: '346766260563-bb6hrbqhcldvcoiaghmr8s54mem7aoi3.apps.googleusercontent.com',
    iosBundleId: 'io.boet.sluqe',
  );
}