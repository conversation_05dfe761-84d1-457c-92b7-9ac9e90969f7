import Flutter
import UIKit
import ActivityKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    if #available(iOS 16.1, *) {
      setupLiveActivities()
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  @available(iOS 16.1, *)
  private func setupLiveActivities() {
    let center = UNUserNotificationCenter.current()

    // Create notification category for recording with stop action
    let stopAction = UNNotificationAction(
      identifier: "STOP_RECORDING",
      title: "Stop Recording",
      options: [.destructive]
    )

    let recordingCategory = UNNotificationCategory(
      identifier: "RECORDING_CATEGORY",
      actions: [stopAction],
      intentIdentifiers: [],
      options: [.customDismissAction]
    )

    center.setNotificationCategories([recordingCategory])

    center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
      if granted {
        print("Live Activities permission granted")
      }
    }
  }

  override func applicationDidEnterBackground(_ application: UIApplication) {
    super.applicationDidEnterBackground(application)
  }

  override func applicationWillEnterForeground(_ application: UIApplication) {
    super.applicationWillEnterForeground(application)
  }

  override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
    return super.application(app, open: url, options: options)
  }
}
