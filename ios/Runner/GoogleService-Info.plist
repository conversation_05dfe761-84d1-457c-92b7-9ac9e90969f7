<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>1051414371573-t78lf64heni6pda136emjuogbpce4r4v.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.1051414371573-t78lf64heni6pda136emjuogbpce4r4v</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>1051414371573-fudueiuj82scho4jkms2mok5c2m5s3p6.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCerj8f8-ctWMNfb8Xs4p6XCaPdLd3TgLs</string>
	<key>GCM_SENDER_ID</key>
	<string>1051414371573</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>io.boet.sluqe</string>
	<key>PROJECT_ID</key>
	<string>sluqe-qa</string>
	<key>STORAGE_BUCKET</key>
	<string>sluqe-qa.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:1051414371573:ios:0a87abb275adbeee7039bc</string>
</dict>
</plist>