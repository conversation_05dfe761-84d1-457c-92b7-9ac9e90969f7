import WidgetKit
import SwiftUI
import ActivityKit

struct RecordingAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var duration: String
        var isRecording: Bool
    }
    
    var appName: String
}

struct RecordingLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: RecordingAttributes.self) { context in
            VStack {
                HStack {
                    Image(systemName: "mic.fill")
                        .foregroundColor(.red)
                    Text("Recording Audio")
                        .font(.headline)
                    Spacer()
                    Text(context.state.duration)
                        .font(.title2)
                        .fontWeight(.bold)
                }
                .padding()
            }
            .background(Color.black)
            .foregroundColor(.white)
            .cornerRadius(12)
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.leading) {
                    HStack {
                        Image(systemName: "mic.fill")
                            .foregroundColor(.red)
                        Text("Recording")
                            .font(.caption)
                    }
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text(context.state.duration)
                        .font(.caption)
                        .fontWeight(.bold)
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Button(action: {
                        // Send stop recording intent to the app
                        if let url = URL(string: "sluqe://stop-recording") {
                            UIApplication.shared.open(url)
                        }
                    }) {
                        HStack {
                            Image(systemName: "stop.fill")
                            Text("Stop Recording")
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(20)
                    }
                }
            } compactLeading: {
                Image(systemName: "mic.fill")
                    .foregroundColor(.red)
            } compactTrailing: {
                Text(context.state.duration)
                    .font(.caption2)
                    .fontWeight(.bold)
            } minimal: {
                Image(systemName: "mic.fill")
                    .foregroundColor(.red)
            }
        }
    }
}

struct RecordingWidget: Widget {
    let kind: String = "RecordingWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            RecordingWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Recording Widget")
        .description("Shows recording status")
    }
}

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), duration: "00:00")
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), duration: "00:00")
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let entries: [SimpleEntry] = []
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let duration: String
}

struct RecordingWidgetEntryView : View {
    var entry: Provider.Entry

    var body: some View {
        VStack {
            HStack {
                Image(systemName: "mic.fill")
                    .foregroundColor(.red)
                Text("Recording")
                Spacer()
                Text(entry.duration)
                    .fontWeight(.bold)
            }
        }
        .padding()
        .background(Color.black)
        .foregroundColor(.white)
    }
}

struct RecordingWidget_Previews: PreviewProvider {
    static var previews: some View {
        RecordingWidgetEntryView(entry: SimpleEntry(date: Date(), duration: "01:23"))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
    }
}
