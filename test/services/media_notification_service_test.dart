import 'package:flutter_test/flutter_test.dart';
import 'package:sluqe/services/record/media_notification_service.dart';

void main() {
  group('MediaNotificationService', () {
    late MediaNotificationService service;

    setUp(() {
      service = MediaNotificationService.instance;
    });

    test('should be a singleton', () {
      final instance1 = MediaNotificationService.instance;
      final instance2 = MediaNotificationService.instance;
      expect(instance1, equals(instance2));
    });

    test('should format duration correctly', () {
      expect(service.formatDuration(Duration(minutes: 1, seconds: 30)), '01:30');
      expect(service.formatDuration(Duration(hours: 1, minutes: 5, seconds: 45)), '1:05:45');
      expect(service.formatDuration(Duration(seconds: 5)), '00:05');
    });

    test('should not be recording initially', () {
      expect(service.isRecording, false);
      expect(service.recordingDuration, Duration.zero);
    });

    test('should be available on supported platforms', () {
      // This test will pass on iOS/Android and fail on other platforms
      // which is expected behavior
      expect(service.isAvailable, isA<bool>());
    });
  });
}
