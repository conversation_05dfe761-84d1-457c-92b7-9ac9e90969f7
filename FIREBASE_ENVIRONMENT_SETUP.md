# Firebase Environment Configuration Setup

This document explains the **AUTOMATIC** Firebase environment switching for your Flutter app.

## ✅ **WORKING SOLUTION - NO MANUAL SCRIPTS NEEDED**

Your app now **automatically** switches Firebase environments:

- **🏪 App Store/Play Store releases**: Uses `sluqe-prod` Firebase project
- **🧪 TestFlight/Local development**: Uses `sluqe-qa` Firebase project

## ✅ **COMPLETED & WORKING:**
- ✅ Fixed Firebase duplicate app error
- ✅ Codemagic automatically switches to production Firebase for store releases
- ✅ Local development uses dev Firebase
- ✅ **NO MANUAL SCRIPTS REQUIRED** - everything happens automatically in CI/CD

## 🔧 **How It Works Automatically**

### **Codemagic CI/CD Pipeline**

When Codemagic builds for **App Store/Play Store**:

1. **🔄 Automatic Firebase Switch**: Codemagic runs `flutterfire configure --project=sluqe-prod`
2. **📱 Production Build**: App gets built with production Firebase configs
3. **🚀 Store Release**: App Store/Play Store users get `sluqe-prod` Firebase

### **Local Development**

When you run locally:
- **🧪 Dev Firebase**: Always uses `sluqe-qa` Firebase project
- **🔧 TestFlight**: Also uses `sluqe-qa` Firebase project

### **The Magic**
- **No environment variables needed**
- **No manual scripts to run**
- **Codemagic handles everything automatically**

## 🎯 **What You Need to Do: NOTHING!**

### ✅ **Setup Complete**

The setup is **100% complete** and working automatically:

1. **✅ Firebase duplicate app error**: FIXED
2. **✅ Codemagic configuration**: DONE - automatically switches to production
3. **✅ Local development**: WORKS - uses dev Firebase
4. **✅ Production releases**: READY - will use production Firebase

### 🚀 **Ready to Use**

- **Push to Codemagic**: Tag your release (e.g., `v1.0.0`) and Codemagic will automatically:
  - Switch to `sluqe-prod` Firebase
  - Build for App Store/Play Store
  - Deploy with production Firebase

- **Local Development**: Just run `flutter run` - uses `sluqe-qa` automatically

## Testing

### Local Development
```bash
# Uses dev Firebase (sluqe-qa)
flutter run

# Force production Firebase
flutter run --dart-define=FIREBASE_ENV=prod
```

### Build Testing
```bash
# Development build (uses sluqe-qa)
flutter build apk --dart-define=FIREBASE_ENV=dev

# Production build (uses sluqe-prod)
flutter build apk --dart-define=FIREBASE_ENV=prod
```

## File Structure

After setup, you'll have these configuration files:

```
android/app/
├── google-services.json              # Current (dev)
├── google-services-dev.json          # Dev backup
└── google-services-prod.json         # Production backup

ios/Runner/
├── GoogleService-Info.plist          # Current (dev)
├── GoogleService-Info-dev.plist      # Dev backup
└── GoogleService-Info-prod.plist     # Production backup

lib/
└── firebase_options.dart             # Contains both dev and prod configs
```

## Troubleshooting

### If builds fail with Firebase errors:
1. Verify the production Firebase project `sluqe-prod` exists
2. Ensure the bundle ID `io.boet.sluqe` is configured in the production project
3. Check that all placeholder values in `firebase_options.dart` are replaced

### To revert to dev-only configuration:
1. Restore from `lib/firebase_options_dev_backup.dart`
2. Remove the `--dart-define=FIREBASE_ENV=prod` from Codemagic builds

## Security Notes

- Production Firebase configurations are now embedded in the app code
- This is standard practice for Firebase client configurations
- Sensitive server-side configurations should remain in Firebase console
