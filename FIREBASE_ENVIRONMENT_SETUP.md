# Firebase Environment Configuration Setup

This document explains how to configure Firebase for different environments (dev and production) in your Flutter app.

## Overview

The app now automatically detects the environment and uses the appropriate Firebase configuration:

- **Production (App Store/Play Store)**: Uses `sluqe-prod` Firebase project
- **Development/TestFlight/Local**: Uses `sluqe-qa` Firebase project

## Current Status

✅ **Completed:**
- Modified `lib/firebase_options.dart` to support environment-based Firebase configuration
- Updated Codemagic CI/CD pipeline to use production Firebase for store releases
- Created backup and setup scripts

⚠️ **Pending:** You need to complete the production Firebase configuration

## How It Works

### Environment Detection Logic

The app determines which Firebase configuration to use based on:

1. **Environment Variable**: If `FIREBASE_ENV` is set to `prod` or `production`, use production config
2. **Build Mode**: Debug builds always use development config
3. **Default**: Release builds without explicit environment variable use production config

### Codemagic Pipeline

The Codemagic pipeline now builds with `--dart-define=FIREBASE_ENV=prod` for all builds, ensuring:
- App Store releases use production Firebase
- Play Store releases use production Firebase

For development/testing builds, you can override this by setting `FIREBASE_ENV=dev`.

## Setup Instructions

### Step 1: Generate Production Firebase Configuration

Run the provided script to safely generate production Firebase configs:

```bash
./scripts/setup_prod_firebase.sh
```

This script will:
1. Backup your current dev configurations
2. Generate production Firebase configuration using `flutterfire configure --project=sluqe-prod`
3. Create separate config files for both environments
4. Restore your dev configurations

### Step 2: Update Production Configuration Values

After running the script:

1. Open `lib/firebase_options_prod.dart` (generated by the script)
2. Copy the production values from this file
3. Replace the placeholder values in `lib/firebase_options.dart`:

```dart
// Replace these placeholder values with actual production values
static const FirebaseOptions androidProd = FirebaseOptions(
  apiKey: 'YOUR_PROD_ANDROID_API_KEY',
  appId: 'YOUR_PROD_ANDROID_APP_ID',
  messagingSenderId: 'YOUR_PROD_MESSAGING_SENDER_ID',
  projectId: 'sluqe-prod',
  storageBucket: 'sluqe-prod.firebasestorage.app',
);

static const FirebaseOptions iosProd = FirebaseOptions(
  apiKey: 'YOUR_PROD_IOS_API_KEY',
  appId: 'YOUR_PROD_IOS_APP_ID',
  messagingSenderId: 'YOUR_PROD_MESSAGING_SENDER_ID',
  projectId: 'sluqe-prod',
  storageBucket: 'sluqe-prod.firebasestorage.app',
  iosClientId: 'YOUR_PROD_IOS_CLIENT_ID',
  iosBundleId: 'io.boet.sluqe',
);
```

### Step 3: Clean Up

After updating the production values:
1. Delete `lib/firebase_options_prod.dart` (no longer needed)
2. Keep the backup config files for reference

## Testing

### Local Development
```bash
# Uses dev Firebase (sluqe-qa)
flutter run

# Force production Firebase
flutter run --dart-define=FIREBASE_ENV=prod
```

### Build Testing
```bash
# Development build (uses sluqe-qa)
flutter build apk --dart-define=FIREBASE_ENV=dev

# Production build (uses sluqe-prod)
flutter build apk --dart-define=FIREBASE_ENV=prod
```

## File Structure

After setup, you'll have these configuration files:

```
android/app/
├── google-services.json              # Current (dev)
├── google-services-dev.json          # Dev backup
└── google-services-prod.json         # Production backup

ios/Runner/
├── GoogleService-Info.plist          # Current (dev)
├── GoogleService-Info-dev.plist      # Dev backup
└── GoogleService-Info-prod.plist     # Production backup

lib/
└── firebase_options.dart             # Contains both dev and prod configs
```

## Troubleshooting

### If builds fail with Firebase errors:
1. Verify the production Firebase project `sluqe-prod` exists
2. Ensure the bundle ID `io.boet.sluqe` is configured in the production project
3. Check that all placeholder values in `firebase_options.dart` are replaced

### To revert to dev-only configuration:
1. Restore from `lib/firebase_options_dev_backup.dart`
2. Remove the `--dart-define=FIREBASE_ENV=prod` from Codemagic builds

## Security Notes

- Production Firebase configurations are now embedded in the app code
- This is standard practice for Firebase client configurations
- Sensitive server-side configurations should remain in Firebase console
