#!/bin/bash

# Script to safely setup production Firebase configuration
# This script generates production Firebase configs without overriding dev configs

set -e

echo "🔥 Setting up production Firebase configuration..."

# Backup current dev configurations
echo "📦 Backing up current dev configurations..."
cp lib/firebase_options.dart lib/firebase_options_dev_backup.dart
cp android/app/google-services.json android/app/google-services-dev.json
cp ios/Runner/GoogleService-Info.plist ios/Runner/GoogleService-Info-dev.plist

echo "✅ Dev configurations backed up"

# Generate production Firebase configuration
echo "🚀 Generating production Firebase configuration..."
echo "Running: flutterfire configure --project=sluqe-prod --out=lib/firebase_options_prod.dart"

# Generate production config in a separate file
flutterfire configure --project=sluqe-prod --out=lib/firebase_options_prod.dart

echo "✅ Production Firebase configuration generated in lib/firebase_options_prod.dart"

# Copy production config files
echo "📋 Copying production config files..."
cp android/app/google-services.json android/app/google-services-prod.json
cp ios/Runner/GoogleService-Info.plist ios/Runner/GoogleService-Info-prod.plist

echo "✅ Production config files copied"

# Restore dev configurations
echo "🔄 Restoring dev configurations..."
cp android/app/google-services-dev.json android/app/google-services.json
cp ios/Runner/GoogleService-Info-dev.plist ios/Runner/GoogleService-Info.plist

echo "✅ Dev configurations restored"

echo ""
echo "🎉 Production Firebase setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Check lib/firebase_options_prod.dart for the production configuration"
echo "2. Copy the production values to lib/firebase_options.dart (androidProd and iosProd)"
echo "3. The script has created backup files:"
echo "   - android/app/google-services-prod.json (production Android config)"
echo "   - ios/Runner/GoogleService-Info-prod.plist (production iOS config)"
echo "   - android/app/google-services-dev.json (dev Android config)"
echo "   - ios/Runner/GoogleService-Info-dev.plist (dev iOS config)"
echo ""
echo "⚠️  Remember to update the placeholder values in lib/firebase_options.dart"
